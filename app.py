from kivy.app import App
from kivy.uix.floatlayout import FloatLayout
from kivy.uix.button import Button
from kivy.uix.label import Label
from kivy.core.window import Window
from kivy.core.text import LabelBase
from kivy.resources import resource_add_path
from kivy.graphics import Rectangle, Color
from kivy.core.image import Image as CoreImage
from kivy.clock import Clock
import os
import platform
import time
import datetime
import subprocess
import threading
if platform.system() == 'Windows':
    import win32gui
    import win32con
    import win32api


# ---------- 字体配置 ----------
# 添加字体资源路径 - 使用系统字体路径
# Windows 7 兼容性处理
if platform.system() == 'Windows':
    try:
        fonts_path = os.path.join(os.environ['WINDIR'], 'Fonts')
        resource_add_path(fonts_path)

        # 检查字体文件是否存在
        msyh_path = os.path.join(fonts_path, 'msyh.ttc')
        simhei_path = os.path.join(fonts_path, 'simhei.ttf')
        simsun_path = os.path.join(fonts_path, 'simsun.ttc')

        if os.path.exists(msyh_path):
            primary_font = msyh_path
        elif os.path.exists(simhei_path):
            primary_font = simhei_path
        elif os.path.exists(simsun_path):
            primary_font = simsun_path
        else:
            # 找不到中文字体时的备用方案
            primary_font = None
            print("警告: 找不到中文字体文件")

        # 注册中文字体
        if primary_font:
            LabelBase.register('chinese_font', primary_font)
    except Exception as e:
        print(f"字体配置错误: {e}")
        # 出错时不注册字体，应用将使用默认字体


class MainWidget(FloatLayout):
    def __init__(self, **kwargs):
        super(MainWidget, self).__init__(**kwargs)
        # 添加防重复点击标志
        self.btn1_clicking = False
        self.btn2_clicking = False

        # 设置背景图片
        with self.canvas.before:
            # 加载背景图片
            try:
                bg_image = CoreImage('picture/3000背景.bmp')
                self.bg_rect = Rectangle(texture=bg_image.texture, pos=self.pos, size=self.size)
                self.bind(size=self._update_bg, pos=self._update_bg)
            except Exception as e:
                print(f"背景图片加载失败: {e}")
                # 使用纯色背景作为备用
                Color(0.2, 0.2, 0.8, 1)  # 蓝色背景
                self.bg_rect = Rectangle(pos=self.pos, size=self.size)
                self.bind(size=self._update_bg, pos=self._update_bg)

        # 创建综合业务按钮 - 使用您提供的按钮图片
        self.btn1 = Button(
            text='',  # 不显示文字，因为图片已包含文字
            background_normal='picture/p1.png',
            background_down='picture/p1.png',
            border=(0, 0, 0, 0),
            size_hint=(None, None),
            pos_hint={'center_x': 0.19, 'center_y': 0.80}  # 调整位置以匹配目标效果
        )

        # 创建个人业务按钮 - 使用您提供的按钮图片
        self.btn2 = Button(
            text='',  # 不显示文字，因为图片已包含文字
            background_normal='picture/p2.png',
            background_down='picture/p2.png',
            border=(0, 0, 0, 0),
            size_hint=(None, None),
            pos_hint={'center_x': 0.19, 'center_y': 0.64}  # 调整位置以匹配目标效果
        )

        # 设置按钮尺寸（根据图片实际尺寸或设置合适的尺寸）
        try:
            img1 = CoreImage('picture/p1.png')
            self.btn1.size = (img1.texture.width, img1.texture.height)

            img2 = CoreImage('picture/p2.png')
            self.btn2.size = (img2.texture.width, img2.texture.height)
        except Exception as e:
            print(f"按钮图片加载失败: {e}")
            # 使用合适的默认尺寸，匹配您提供的按钮图片样式
            self.btn1.size = (300, 80)  # 调整为更合适的尺寸
            self.btn2.size = (300, 80)

        # 绑定按钮事件
        self.btn1.bind(on_press=self.on_btn1_press)
        self.btn2.bind(on_press=self.on_btn2_press)

        # 添加按钮到布局
        self.add_widget(self.btn1)
        self.add_widget(self.btn2)

        # 创建时间显示标签
        # 检查是否有中文字体可用
        try:
            font_name = 'chinese_font'
        except:
            font_name = 'Roboto'

        self.time_label = Label(
            text=self.get_current_time(),
            font_name=font_name,
            font_size='16sp',
            color=(0, 0, 0, 1),  # 白色文字
            size_hint=(None, None),
            size=(200, 30),
            pos_hint={'right': 0.98, 'top': 0.98}  # 右上角位置
        )
        self.add_widget(self.time_label)

        # 启动时间更新定时器，每秒更新一次
        Clock.schedule_interval(self.update_time, 1)

    def get_current_time(self):
        """获取当前时间，格式：2025年07月16日 17:05:03"""
        now = datetime.datetime.now()
        return now.strftime("%Y年%m月%d日 %H:%M:%S")

    def update_time(self, dt):
        """更新时间显示"""
        self.time_label.text = self.get_current_time()

    def _update_bg(self, instance, value):
        """更新背景图片尺寸和位置"""
        if hasattr(self, 'bg_rect'):
            self.bg_rect.pos = instance.pos
            self.bg_rect.size = instance.size

    def on_btn1_press(self, instance):
        """综合业务按钮点击事件"""
        # 忽略instance参数，仅用于事件回调
        # 防止重复点击
        if self.btn1_clicking:
            print("综合业务按钮正在处理中，忽略重复点击")
            return

        self.btn1_clicking = True
        print("=" * 50)
        print("综合业务按钮被点击 - 开始处理流程")
        print("=" * 50)

        try:
            # 获取当前按钮在屏幕上的位置
            print("步骤1: 获取按钮屏幕位置...")
            btn_pos = self.get_button_screen_position(self.btn1)
            if btn_pos:
                print(f"步骤2: 按钮位置获取成功: {btn_pos}")
                # 切换到外部程序并在相同位置点击
                app = App.get_running_app()
                if hasattr(app, 'switch_to_external_and_click'):
                    print("步骤3: 开始切换到外部程序...")
                    app.switch_to_external_and_click(btn_pos[0], btn_pos[1], '综合业务')
                else:
                    print("错误: 应用没有切换方法")
            else:
                print("错误: 无法获取按钮位置")
        except Exception as e:
            print(f"综合业务按钮处理异常: {e}")
        finally:
            # 延迟重置点击标志，防止快速重复点击
            threading.Timer(3.0, self.reset_btn1_clicking).start()

    def on_btn2_press(self, instance):
        """个人业务按钮点击事件"""
        # 忽略instance参数，仅用于事件回调
        # 防止重复点击
        if self.btn2_clicking:
            print("个人业务按钮正在处理中，忽略重复点击")
            return

        self.btn2_clicking = True
        print("=" * 50)
        print("个人业务按钮被点击 - 开始处理流程")
        print("=" * 50)

        try:
            # 获取当前按钮在屏幕上的位置
            print("步骤1: 获取按钮屏幕位置...")
            btn_pos = self.get_button_screen_position(self.btn2)
            if btn_pos:
                print(f"步骤2: 按钮位置获取成功: {btn_pos}")
                # 切换到外部程序并在相同位置点击
                app = App.get_running_app()
                if hasattr(app, 'switch_to_external_and_click'):
                    print("步骤3: 开始切换到外部程序...")
                    app.switch_to_external_and_click(btn_pos[0], btn_pos[1], '个人业务')
                else:
                    print("错误: 应用没有切换方法")
            else:
                print("错误: 无法获取按钮位置")
        except Exception as e:
            print(f"个人业务按钮处理异常: {e}")
        finally:
            # 延迟重置点击标志，防止快速重复点击
            threading.Timer(3.0, self.reset_btn2_clicking).start()

    def reset_btn1_clicking(self):
        """重置按钮1点击标志"""
        self.btn1_clicking = False
        print("综合业务按钮点击标志已重置")

    def reset_btn2_clicking(self):
        """重置按钮2点击标志"""
        self.btn2_clicking = False
        print("个人业务按钮点击标志已重置")

    def get_button_screen_position(self, button):
        """获取按钮在屏幕上的中心位置"""
        try:
            # 获取按钮在窗口中的位置
            btn_x = button.x + button.width / 2
            btn_y = button.y + button.height / 2

            # 转换为屏幕坐标
            # Kivy的坐标系是左下角为原点，Windows是左上角为原点
            from kivy.core.window import Window

            # 获取窗口在屏幕上的位置
            if platform.system() == 'Windows':
                try:
                    # 获取当前窗口句柄
                    app = App.get_running_app()
                    if hasattr(app, 'get_current_window_handle'):
                        hwnd = app.get_current_window_handle()
                        if hwnd:
                            # 获取窗口在屏幕上的位置
                            rect = win32gui.GetWindowRect(hwnd)
                            window_x, window_y = rect[0], rect[1]

                            # 计算按钮在屏幕上的绝对位置
                            screen_x = int(window_x + btn_x)
                            screen_y = int(window_y + (Window.height - btn_y))  # 转换Y坐标
                        else:
                            # 备用方案：假设窗口在屏幕左上角
                            screen_x = int(btn_x)
                            screen_y = int(Window.height - btn_y)
                    else:
                        # 备用方案：假设窗口在屏幕左上角
                        screen_x = int(btn_x)
                        screen_y = int(Window.height - btn_y)
                except Exception as e:
                    print(f"获取窗口位置失败，使用备用方案: {e}")
                    screen_x = int(btn_x)
                    screen_y = int(Window.height - btn_y)
            else:
                screen_x = int(btn_x)
                screen_y = int(Window.height - btn_y)

            print(f"按钮屏幕位置: ({screen_x}, {screen_y})")
            return (screen_x, screen_y)
        except Exception as e:
            print(f"获取按钮位置失败: {e}")
            return None




class QueueApp(App):
    def __init__(self, **kwargs):
        super(QueueApp, self).__init__(**kwargs)
        self.external_process = None

    def build(self):
        # 设置窗口标题
        self.title = '智能排队管理系统'

        # 设置窗口为全屏无边框 - Windows 7 兼容性优化
        try:
            # 获取屏幕尺寸
            from kivy.core.window import Window

            # 设置全屏模式
            Window.fullscreen = 'auto'  # 自适应全屏
            Window.borderless = True    # 无边框

            # Windows 7 兼容性设置
            Window.always_on_top = False  # 不总是置顶
            Window.resizable = False      # 不可调整大小

            # 设置窗口背景色（备用）
            Window.clearcolor = (0.2, 0.2, 0.8, 1)  # 蓝色背景

            # 绑定窗口显示事件
            Window.bind(on_show=self.on_window_show)

        except Exception as e:
            print(f"窗口属性设置错误: {e}")

        return MainWidget()

    def on_window_show(self, window):
        """当窗口显示时的回调函数"""
        # 忽略window参数，仅用于事件回调
        if platform.system() == 'Windows':
            print("窗口显示事件触发，准备管理窗口...")
            # 延迟执行窗口管理，确保窗口完全显示
            threading.Timer(1.0, self.manage_windows).start()

    def manage_windows(self):
        """管理窗口显示：隐藏外部程序，显示当前窗口"""
        if platform.system() == 'Windows':
            try:
                print("开始执行窗口管理...")

                # 先确保当前窗口显示在前台
                self.bring_to_front()

                # 稍等一下再隐藏外部程序，确保当前窗口已经完全显示
                threading.Timer(0.5, self.hide_external_program).start()

            except Exception as e:
                print(f"窗口管理错误: {e}")

    def on_start(self):
        """应用启动完成后的回调"""
        print("Kivy应用启动完成")
        if platform.system() == 'Windows':
            # 应用启动完成后也执行一次窗口管理
            threading.Timer(2.0, self.manage_windows).start()

    def hide_external_program(self):
        """隐藏外部程序窗口"""
        try:
            # 定义需要隐藏的程序关键词
            external_keywords = [
                'getnob',
                'navicat',
                'queue',
                'server2012'
            ]

            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd).lower()
                    class_name = win32gui.GetClassName(hwnd).lower()

                    # 检查窗口标题或类名是否包含外部程序关键词
                    for keyword in external_keywords:
                        if keyword in window_text or keyword in class_name:
                            # 排除当前Kivy应用窗口
                            if '智能排队管理系统' not in win32gui.GetWindowText(hwnd):
                                windows.append((hwnd, win32gui.GetWindowText(hwnd)))
                            break
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            # 隐藏找到的窗口
            hidden_count = 0
            for hwnd, window_title in windows:
                try:
                    win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                    print(f"隐藏外部程序窗口: {window_title}")
                    hidden_count += 1
                except Exception as e:
                    print(f"隐藏窗口失败 {window_title}: {e}")

            if hidden_count == 0:
                print("未找到需要隐藏的外部程序窗口")
            else:
                print(f"成功隐藏 {hidden_count} 个外部程序窗口")

        except Exception as e:
            print(f"隐藏外部程序失败: {e}")

    def hide_current_window(self):
        """隐藏当前窗口"""
        try:
            # 查找当前Kivy应用窗口
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)

                    # 根据窗口标题或类名识别当前应用
                    if ('智能排队管理系统' in window_text or
                        'kivy' in class_name.lower() or
                        'python' in window_text.lower()):
                        windows.append((hwnd, window_text))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            # 隐藏窗口
            for hwnd, window_title in windows:
                try:
                    win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
                    print(f"隐藏当前窗口: {window_title}")
                    break  # 只处理第一个找到的窗口
                except Exception as e:
                    print(f"隐藏窗口失败 {window_title}: {e}")

        except Exception as e:
            print(f"隐藏当前窗口失败: {e}")

    def bring_to_front(self):
        """将当前窗口置于前台"""
        try:
            # 查找当前Kivy应用窗口
            def enum_windows_callback(hwnd, windows):
                # 检查所有窗口，包括隐藏的
                window_text = win32gui.GetWindowText(hwnd)
                class_name = win32gui.GetClassName(hwnd)

                # 根据窗口标题或类名识别当前应用
                if ('智能排队管理系统' in window_text or
                    'kivy' in class_name.lower() or
                    'python' in window_text.lower()):
                    windows.append((hwnd, window_text))
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            # 将窗口置于前台
            brought_to_front = False
            for hwnd, window_title in windows:
                try:
                    # 先显示窗口
                    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                    # 然后最大化
                    win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
                    # 最后置于前台
                    win32gui.SetForegroundWindow(hwnd)
                    print(f"将窗口置于前台: {window_title}")
                    brought_to_front = True
                    break  # 只处理第一个找到的窗口
                except Exception as e:
                    print(f"置前窗口失败 {window_title}: {e}")

            if not brought_to_front:
                print("未找到需要置前的应用窗口")

        except Exception as e:
            print(f"窗口置前失败: {e}")

    def start_external_program(self):
        """启动外部程序"""
        try:
            # 可以配置多个可能的外部程序路径
            external_programs = [
                "C:/Program Files/ZdBit/Queue/Server2012/getnob.exe"
                # 可以添加更多程序路径
            ]

            for external_exe in external_programs:
                if os.path.exists(external_exe):
                    self.external_process = subprocess.Popen(external_exe)
                    print(f"启动外部程序: {external_exe}")
                    return True

            print("未找到可用的外部程序")
            return False
        except Exception as e:
            print(f"启动外部程序失败: {e}")
            return False

    def switch_to_external_and_click(self, x, y, button_id):
        """切换到外部程序，在指定位置点击，然后切换回当前界面"""
        if platform.system() == 'Windows':
            try:
                print(f"准备切换到外部程序并在位置 ({x}, {y}) 点击 - 按钮: {button_id}")

                # 1. 记录当前窗口状态
                current_hwnd = self.get_current_window_handle()
                if not current_hwnd:
                    print("无法获取当前窗口句柄")
                    return

                # 2. 先隐藏当前窗口，避免点击冲突
                print("隐藏当前界面...")
                self.hide_current_window()

                # 3. 等待窗口隐藏完成
                time.sleep(0.3)

                # 4. 显示外部程序窗口
                print("显示外部程序...")
                external_hwnd = self.show_external_program()
                if not external_hwnd:
                    print("未找到外部程序窗口，恢复当前界面")
                    # 如果没找到外部程序，重新显示当前窗口
                    self.bring_to_front()
                    return

                # 5. 等待窗口切换完成
                time.sleep(0.6)

                # 6. 在指定位置进行鼠标点击
                print(f"在外部程序中点击位置 ({x}, {y})...")
                self.click_at_position(x, y)

                # 7. 等待点击处理完成
                time.sleep(0.4)

                # 8. 切换回当前界面
                print("切换回当前界面...")
                self.bring_to_front()

                # 9. 等待当前界面完全显示后再隐藏外部程序
                threading.Timer(0.8, self.hide_external_program).start()

                print(f"外部程序点击操作完成 - 按钮: {button_id}")

            except Exception as e:
                print(f"切换到外部程序并点击失败: {e}")
                # 出错时确保当前窗口可见
                print("发生错误，恢复当前界面...")
                self.bring_to_front()

    def get_current_window_handle(self):
        """获取当前Kivy应用窗口句柄"""
        try:
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd) or True:  # 包括隐藏的窗口
                    window_text = win32gui.GetWindowText(hwnd)
                    class_name = win32gui.GetClassName(hwnd)

                    # 根据窗口标题或类名识别当前应用
                    if ('智能排队管理系统' in window_text or
                        'kivy' in class_name.lower() or
                        'python' in window_text.lower()):
                        windows.append(hwnd)
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            if windows:
                return windows[0]  # 返回第一个找到的窗口句柄
            return None
        except Exception as e:
            print(f"获取窗口句柄失败: {e}")
            return None

    def show_external_program(self):
        """显示外部程序窗口"""
        try:
            # 定义外部程序关键词
            external_keywords = [
                'getnob',
                'navicat',
                'queue',
                'server2012',
                'zdbit'  # 添加可能的公司名称
            ]

            def enum_windows_callback(hwnd, windows):
                # 检查所有窗口，包括隐藏的窗口
                window_text = win32gui.GetWindowText(hwnd).lower()
                class_name = win32gui.GetClassName(hwnd).lower()

                # 检查窗口标题或类名是否包含外部程序关键词
                for keyword in external_keywords:
                    if keyword in window_text or keyword in class_name:
                        # 排除当前Kivy应用窗口
                        if '智能排队管理系统' not in win32gui.GetWindowText(hwnd):
                            # 保存窗口句柄和标题
                            windows.append((hwnd, win32gui.GetWindowText(hwnd)))
                        break
                return True

            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)

            # 如果找到了窗口
            if windows:
                print(f"找到 {len(windows)} 个外部程序窗口")

                # 显示找到的第一个外部程序窗口
                for hwnd, window_title in windows:
                    try:
                        # 检查窗口是否有效
                        if win32gui.IsWindow(hwnd):
                            # 显示窗口
                            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                            time.sleep(0.1)
                            win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
                            time.sleep(0.1)
                            win32gui.SetForegroundWindow(hwnd)
                            print(f"显示外部程序窗口: {window_title}")
                            return hwnd
                        else:
                            print(f"窗口无效: {window_title}")
                    except Exception as e:
                        print(f"显示窗口失败 {window_title}: {e}")
            else:
                # 如果没有找到窗口，尝试启动外部程序
                print("未找到外部程序窗口，尝试启动外部程序...")
                if self.start_external_program():
                    # 等待程序启动
                    time.sleep(3)
                    # 重新查找窗口
                    windows = []
                    win32gui.EnumWindows(enum_windows_callback, windows)

                    # 显示找到的第一个外部程序窗口
                    for hwnd, window_title in windows:
                        try:
                            if win32gui.IsWindow(hwnd):
                                win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
                                win32gui.ShowWindow(hwnd, win32con.SW_MAXIMIZE)
                                win32gui.SetForegroundWindow(hwnd)
                                print(f"显示新启动的外部程序窗口: {window_title}")
                                return hwnd
                        except Exception as e:
                            print(f"显示新启动窗口失败 {window_title}: {e}")

            print("未找到可显示的外部程序窗口")
            return None

        except Exception as e:
            print(f"显示外部程序失败: {e}")
            return None

    def click_at_position(self, x, y):
        """在指定位置进行鼠标点击"""
        try:
            print(f"准备在位置 ({x}, {y}) 执行鼠标点击")

            # 移动鼠标到指定位置
            win32api.SetCursorPos((x, y))
            time.sleep(0.15)  # 增加等待时间确保鼠标移动完成

            # 验证鼠标位置
            current_pos = win32api.GetCursorPos()
            print(f"鼠标当前位置: {current_pos}")

            # 执行鼠标左键点击
            # 使用数值常量，因为某些版本的win32con可能没有这些常量
            MOUSEEVENTF_LEFTDOWN = 0x0002
            MOUSEEVENTF_LEFTUP = 0x0004

            # 按下鼠标左键
            win32api.mouse_event(MOUSEEVENTF_LEFTDOWN, x, y, 0, 0)
            time.sleep(0.08)  # 稍微增加按键持续时间

            # 释放鼠标左键
            win32api.mouse_event(MOUSEEVENTF_LEFTUP, x, y, 0, 0)

            print(f"在位置 ({x}, {y}) 执行鼠标点击完成")

        except Exception as e:
            print(f"鼠标点击失败: {e}")
            # 尝试备用点击方法
            try:
                print("尝试备用点击方法...")
                import ctypes
                ctypes.windll.user32.SetCursorPos(x, y)
                time.sleep(0.1)
                ctypes.windll.user32.mouse_event(2, 0, 0, 0, 0)  # 左键按下
                time.sleep(0.05)
                ctypes.windll.user32.mouse_event(4, 0, 0, 0, 0)  # 左键释放
                print("备用点击方法执行完成")
            except Exception as e2:
                print(f"备用点击方法也失败: {e2}")

    def on_stop(self):
        """应用程序停止时的清理工作"""
        try:
            # 如果外部程序仍在运行，可以选择是否终止它
            if self.external_process and self.external_process.poll() is None:
                print("外部程序仍在运行，保持运行状态")
                # 如果需要终止外部程序，取消注释下面的行
                # self.external_process.terminate()
        except Exception as e:
            print(f"清理外部程序失败: {e}")


def start_external_program_first():
    """在主程序启动前先启动外部程序"""
    try:
        # 可以配置多个可能的外部程序路径
        external_programs = [
            "C:/Program Files/ZdBit/Queue/Server2012/getnob.exe"
            # 可以添加更多程序路径
        ]

        for external_exe in external_programs:
            if os.path.exists(external_exe):
                print(f"正在启动外部程序: {external_exe}")
                process = subprocess.Popen(external_exe)
                print(f"外部程序已启动，PID: {process.pid}")

                # 等待外部程序完全启动
                time.sleep(3)
                return process

        print("未找到可用的外部程序，继续启动主程序")
        return None
    except Exception as e:
        print(f"启动外部程序失败: {e}")
        return None


if __name__ == '__main__':
    print("=== 智能排队管理系统启动 ===")

    # 第一步：启动外部程序
    print("1. 启动外部程序...")
    external_process = start_external_program_first()

    # 第二步：启动Kivy应用
    print("2. 启动主界面...")
    try:
        app = QueueApp()
        app.external_process = external_process  # 保存外部程序进程引用

        # 启动Kivy应用（这会自动处理窗口管理）
        app.run()

    except Exception as e:
        print(f"应用程序运行错误: {e}")
        input("按回车键退出...")  # 方便调试时查看错误信息